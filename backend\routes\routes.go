package routes

import (
	"cx/config"
	"cx/controller"
	"cx/middleware"
	"cx/repository"
	"cx/service"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

func SetupRouter(db *gorm.DB, wpDB *gorm.DB) *gin.Engine {
	serverName := config.AppConfig.Server.ServerName

	router := gin.Default()
	router.Static("/api/uploads", "./uploads")

	r := router.Group(serverName + "/api")

	userRepo := repository.NewUserRepository(db)
	userService := service.NewUserService(db, userRepo)

	tokenRepo := repository.NewTokenRepository(db)
	authService := service.NewAuthService(db, tokenRepo, userRepo)

	controller.NewAuthController(r, authService, userService)

	tokenAuthMiddleware := middleware.TokenAuth(authService, userService)

	xeroService := service.NewXeroService(db)
	controller.NewXeroController(r, xeroService, tokenAuthMiddleware)

	announcementRepo := repository.NewAnnouncementRepository(db)
	announcementService := service.NewAnnouncementService(db, announcementRepo)
	controller.NewAnnouncementController(r, announcementService, tokenAuthMiddleware)

	r.Use(tokenAuthMiddleware)

	controller.NewUserController(r, userService)

	permissionRepo := repository.NewPermissionRepository(db)
	permissionService := service.NewPermissionService(db, permissionRepo)
	controller.NewPermissionController(r, permissionService)

	userGroupRepo := repository.NewUserGroupRepository(db)
	userGroupService := service.NewUserGroupService(db, userGroupRepo, permissionRepo)
	controller.NewUserGroupController(r, userGroupService)

	clockRepo := repository.NewClockRepository(db)
	clockService := service.NewClockService(db, clockRepo, userRepo)
	controller.NewClockController(r, clockService)

	salaryItemRepo := repository.NewSalaryItemRepository(db)
	salaryItemService := service.NewSalaryItemService(db, salaryItemRepo)
	controller.NewSalaryItemController(r, salaryItemService)
	payrollRepo := repository.NewPayrollRepository(db)
	payrollEmailRepo := repository.NewPayrollEmailRepository(db)
	payrollService := service.NewPayrollService(db, userRepo, payrollRepo, payrollEmailRepo)
	controller.NewPayrollController(r, payrollService)

	customerRepo := repository.NewCustomerRepository(db)
	customerService := service.NewCustomerService(db, customerRepo)
	controller.NewCustomerController(r, customerService)

	productRepo := repository.NewProductRepository(db)
	productService := service.NewProductService(db, wpDB, productRepo)
	controller.NewProductController(r, productService)

	productCategoryRepo := repository.NewProductCategoryRepository(db)
	productCategoryService := service.NewProductCategoryService(db, wpDB, productCategoryRepo)
	controller.NewProductCategoryController(r, productCategoryService)

	inventoryRepo := repository.NewInventoryRepository(db, wpDB)
	purchaseOrderRepo := repository.NewPurchaseOrderRepository(db)
	returnRepo := repository.NewReturnRepository(db)
	inventoryService := service.NewInventoryService(db, wpDB, customerRepo, productRepo, inventoryRepo, purchaseOrderRepo, returnRepo)
	controller.NewInventoryController(r, inventoryService)

	orderRepo := repository.NewOrderRepository(db)
	orderItemRepo := repository.NewOrderItemRepository(db)
	orderService := service.NewOrderService(db, wpDB, orderRepo, orderItemRepo, productRepo, inventoryRepo, customerRepo, xeroService)
	controller.NewOrderController(r, orderService, userService)

	// WooCommerce 訂單相關
	wcOrderRepo := repository.NewWcOrderRepository(wpDB)
	wcOrderService := service.NewWcOrderService(wpDB, wcOrderRepo)
	controller.NewWcOrderController(r, wcOrderService, xeroService)

	systemRepo := repository.NewSystemRepository(db)
	systemService := service.NewSystemService(db, systemRepo)
	controller.NewSystemController(r, systemService)

	return router
}
