package controller

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"cx/domain"
	"cx/service"
	"cx/utils"
)

type WcOrderController struct {
	wcOrderService service.WcOrderService
}

func NewWcOrderController(r *gin.RouterGroup, wcOrderService service.WcOrderService) {
	wcOrderController := WcOrderController{wcOrderService}

	v1 := r.Group("/v1/wc-orders")
	{
		v1.GET("/history", wcOrderController.GetHistoryOrdersHandler)
		v1.GET("/pending", wcOrderController.GetPendingOrdersHandler)
		v1.GET("/:id", wcOrderController.GetOrderHandler)
		v1.GET("", wcOrderController.ListOrdersHandler)
		v1.PATCH("/:id/status", wcOrderController.UpdateOrderStatusHandler)
		v1.PATCH("/:id/customer-note", wcOrderController.UpdateCustomerNoteHandler)
	}
}

// GetHistoryOrdersHandler 獲取歷史訂單列表
func (ctr *WcOrderController) GetHistoryOrdersHandler(c *gin.Context) {
	req := struct {
		Filter     domain.WcOrderFilter `form:"filter"`
		Pagination domain.Pagination    `form:"pagination"`
	}{}

	c.ShouldBindQuery(&req)

	orders, err := ctr.wcOrderService.GetHistoryOrders(c, &req.Filter, &req.Pagination)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to fetch history orders")
		return
	}

	utils.HandleSuccess(c, gin.H{
		"data":       convertToOrderSummary(orders),
		"pagination": req.Pagination,
	})
}

// GetPendingOrdersHandler 獲取待處理訂單列表
func (ctr *WcOrderController) GetPendingOrdersHandler(c *gin.Context) {
	orders, err := ctr.wcOrderService.GetPendingOrders(c)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to fetch pending orders")
		return
	}

	utils.HandleSuccess(c, gin.H{
		"data": convertToOrderSummary(orders),
	})
}

// GetOrderHandler 獲取單個訂單詳細信息
func (ctr *WcOrderController) GetOrderHandler(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Invalid order ID")
		return
	}

	order, err := ctr.wcOrderService.GetByID(c.Request.Context(), uint(id))
	if err != nil {
		utils.HandleError(c, http.StatusNotFound, err, "Order not found")
		return
	}

	utils.HandleSuccess(c, order)
}

// ListOrdersHandler 獲取訂單列表（帶分頁和過濾）
func (ctr *WcOrderController) ListOrdersHandler(c *gin.Context) {
	var filter domain.WcOrderFilter

	// 解析查詢參數
	if status := c.Query("status"); status != "" {
		filter.Status = status
	}

	if customerIDStr := c.Query("customer_id"); customerIDStr != "" {
		if customerID, err := strconv.ParseUint(customerIDStr, 10, 32); err == nil {
			filter.CustomerID = uint(customerID)
		}
	}

	if pageStr := c.Query("page"); pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil && page > 0 {
			filter.Page = page
		}
	}

	if pageSizeStr := c.Query("page_size"); pageSizeStr != "" {
		if pageSize, err := strconv.Atoi(pageSizeStr); err == nil && pageSize > 0 {
			filter.PageSize = pageSize
		}
	}

	orders, total, err := ctr.wcOrderService.List(c, &filter)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to fetch orders")
		return
	}

	utils.HandleSuccess(c, gin.H{
		"orders":    orders,
		"total":     total,
		"page":      filter.Page,
		"page_size": filter.PageSize,
	})
}

// UpdateOrderStatusHandler 更新訂單狀態
func (ctr *WcOrderController) UpdateOrderStatusHandler(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Invalid order ID")
		return
	}

	var req struct {
		Status string `json:"status" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		// 也支持查詢參數方式（兼容原有的 PHP API）
		if status := c.Query("status"); status != "" {
			req.Status = status
		} else {
			utils.HandleError(c, http.StatusBadRequest, err, "Invalid request body")
			return
		}
	}

	err = ctr.wcOrderService.UpdateStatus(c, uint(id), req.Status)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to update order status")
		return
	}

	utils.HandleSuccess(c, nil)
}

// convertToOrderSummary 將完整訂單轉換為摘要格式
func convertToOrderSummary(orders []domain.WcOrder) []domain.WcOrderSummary {
	summaries := make([]domain.WcOrderSummary, len(orders))
	for i, order := range orders {
		customerName := order.BillingFirstName + " " + order.BillingLastName
		if customerName == " " {
			customerName = "Guest"
		}

		summaries[i] = domain.WcOrderSummary{
			ID:                  order.ID,
			DateCreated:         order.DateCreated.Format("2006-01-02 15:04:05"),
			Status:              string(order.Status),
			Total:               order.Total,
			PaymentMethodTitle:  order.PaymentMethodTitle,
			CustomerEmail:       order.BillingEmail,
			CustomerName:        customerName,
			ShippingMethod:      order.ShippingMethod,
			ShippingMethodTitle: order.ShippingMethodTitle,
		}
	}
	return summaries
}

// UpdateCustomerNoteHandler 更新訂單客戶備註
func (ctr *WcOrderController) UpdateCustomerNoteHandler(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Invalid order ID")
		return
	}

	var req struct {
		CustomerNote string `json:"customer_note" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Invalid request body")
		return
	}

	err = ctr.wcOrderService.UpdateCustomerNote(c, uint(id), req.CustomerNote)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to update customer note")
		return
	}

	utils.HandleSuccess(c, gin.H{
		"success": true,
		"message": "Customer note updated successfully",
	})
}
