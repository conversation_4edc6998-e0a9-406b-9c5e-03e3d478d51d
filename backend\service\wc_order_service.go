package service

import (
	"context"
	"fmt"
	"strings"

	"gorm.io/gorm"

	"cx/domain"
	"cx/repository"
)

type WcOrderService interface {
	GetByID(ctx context.Context, id uint) (*domain.WcOrder, error)
	List(ctx context.Context, filter *domain.WcOrderFilter) ([]domain.WcOrder, int, error)
	UpdateStatus(ctx context.Context, id uint, status string) error
	UpdateCustomerNote(ctx context.Context, id uint, customerNote string) error
	GetPendingOrders(ctx context.Context) ([]domain.WcOrder, error)
	GetHistoryOrders(ctx context.Context, filter *domain.WcOrderFilter, pagination *domain.Pagination) ([]domain.WcOrder, error)
	GetPaidOrders(ctx context.Context) ([]domain.WcOrder, error)
}

type wcOrderService struct {
	db          *gorm.DB
	wcOrderRepo repository.WcOrderRepository
}

func NewWcOrderService(db *gorm.DB, wcOrderRepo repository.WcOrderRepository) WcOrderService {
	return &wcOrderService{
		db:          db,
		wcOrderRepo: wcOrderRepo,
	}
}

func (s *wcOrderService) GetByID(ctx context.Context, id uint) (*domain.WcOrder, error) {
	if id == 0 {
		return nil, fmt.Errorf("invalid order ID")
	}

	order, err := s.wcOrderRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get order: %w", err)
	}

	return order, nil
}

func (s *wcOrderService) List(ctx context.Context, filter *domain.WcOrderFilter) ([]domain.WcOrder, int, error) {
	if filter == nil {
		filter = &domain.WcOrderFilter{}
	}

	// 設置默認分頁參數
	if filter.Page <= 0 {
		filter.Page = 1
	}
	if filter.PageSize <= 0 {
		filter.PageSize = 20
	}
	if filter.PageSize > 100 {
		filter.PageSize = 100 // 限制最大頁面大小
	}

	orders, total, err := s.wcOrderRepo.List(filter)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list orders: %w", err)
	}

	return orders, total, nil
}

func (s *wcOrderService) UpdateStatus(ctx context.Context, id uint, status string) error {
	if id == 0 {
		return fmt.Errorf("invalid order ID")
	}

	if status == "" {
		return fmt.Errorf("status cannot be empty")
	}

	// 移除可能的空白字符
	status = strings.TrimSpace(status)

	// 確保狀態包含 'wc-' 前綴
	if !strings.HasPrefix(status, "wc-") {
		status = "wc-" + status
	}

	// 驗證狀態是否有效
	validStatuses := map[string]bool{
		"wc-pending":    true,
		"wc-processing": true,
		"wc-on-hold":    true,
		"wc-packing":    true,
		"wc-shipping":   true,
		"wc-completed":  true,
		"wc-cancelled":  true,
		"wc-refunded":   true,
		"wc-failed":     true,
	}

	if !validStatuses[status] {
		return fmt.Errorf("invalid order status: %s", status)
	}

	// 檢查訂單是否存在
	_, err := s.wcOrderRepo.GetByID(id)
	if err != nil {
		return fmt.Errorf("order not found: %w", err)
	}

	err = s.wcOrderRepo.UpdateStatus(id, status)
	if err != nil {
		return fmt.Errorf("failed to update order status: %w", err)
	}

	return nil
}

func (s *wcOrderService) UpdateCustomerNote(ctx context.Context, id uint, customerNote string) error {
	if id == 0 {
		return fmt.Errorf("invalid order ID")
	}

	// 檢查訂單是否存在
	order, err := s.wcOrderRepo.GetByID(id)
	if err != nil {
		return fmt.Errorf("order not found: %w", err)
	}

	// 新增除錯資訊
	fmt.Printf("Updating customer note for order ID: %d, current status: %s, note: %s\n", id, order.Status, customerNote)

	err = s.wcOrderRepo.UpdateCustomerNote(id, customerNote)
	if err != nil {
		return fmt.Errorf("failed to update customer note: %w", err)
	}

	return nil
}

func (s *wcOrderService) GetPendingOrders(ctx context.Context) ([]domain.WcOrder, error) {
	// 使用標準查詢獲取待處理訂單 - 參考 wcapi.php 的 check_new_orders 邏輯
	orders, err := s.wcOrderRepo.GetPendingOrders()
	if err != nil {
		return nil, fmt.Errorf("failed to get pending orders: %w", err)
	}

	return orders, nil
}

func (s *wcOrderService) GetHistoryOrders(ctx context.Context, filter *domain.WcOrderFilter, pagination *domain.Pagination) ([]domain.WcOrder, error) {
	orders, err := s.wcOrderRepo.GetHistoryOrders(filter, pagination)
	if err != nil {
		return nil, fmt.Errorf("failed to get history orders: %w", err)
	}

	return orders, nil
}

func (s *wcOrderService) GetPaidOrders(ctx context.Context) ([]domain.WcOrder, error) {
	// 獲取已付款但尚未創建Xero發票的訂單
	orders, err := s.wcOrderRepo.GetPaidOrders()
	if err != nil {
		return nil, fmt.Errorf("failed to get paid orders without Xero invoice: %w", err)
	}

	return orders, nil
}
