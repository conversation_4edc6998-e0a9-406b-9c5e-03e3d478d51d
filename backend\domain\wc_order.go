package domain

import "time"

type WcOrder struct {
	ID                  uint          `json:"id"`
	OrderNumber         string        `json:"order_number"`
	Status              OrderStatus   `json:"status"`
	Currency            string        `json:"currency"`
	CustomerID          uint          `json:"customer_id"`
	CustomerNote        string        `json:"customer_note"`
	BillingFirstName    string        `json:"billing_first_name"`
	BillingLastName     string        `json:"billing_last_name"`
	BillingCompany      string        `json:"billing_company"`
	BillingAddress1     string        `gorm:"column:billing_address_1" json:"billing_address_1"`
	BillingAddress2     string        `gorm:"column:billing_address_2" json:"billing_address_2"`
	BillingCity         string        `json:"billing_city"`
	BillingState        string        `json:"billing_state"`
	BillingPostcode     string        `json:"billing_postcode"`
	BillingCountry      string        `json:"billing_country"`
	BillingEmail        string        `json:"billing_email"`
	BillingPhone        string        `json:"billing_phone"`
	ShippingFirstName   string        `json:"shipping_first_name"`
	ShippingLastName    string        `json:"shipping_last_name"`
	ShippingCompany     string        `json:"shipping_company"`
	ShippingAddress1    string        `gorm:"column:shipping_address_1" json:"shipping_address_1"`
	ShippingAddress2    string        `gorm:"column:shipping_address_2" json:"shipping_address_2"`
	ShippingCity        string        `json:"shipping_city"`
	ShippingState       string        `json:"shipping_state"`
	ShippingPostcode    string        `json:"shipping_postcode"`
	ShippingCountry     string        `json:"shipping_country"`
	PaymentMethod       string        `json:"payment_method"`
	PaymentMethodTitle  string        `json:"payment_method_title"`
	TransactionID       string        `json:"transaction_id"`
	DateCreated         time.Time     `json:"date_created"`
	DateModified        time.Time     `json:"date_modified"`
	DiscountTotal       float64       `json:"discount_total"`
	DiscountTax         float64       `json:"discount_tax"`
	ShippingTotal       float64       `json:"shipping_total"`
	ShippingTax         float64       `json:"shipping_tax"`
	ShippingMethod      string        `json:"shipping_method"`
	ShippingMethodTitle string        `json:"shipping_method_title"`
	CartTax             float64       `json:"cart_tax"`
	Total               float64       `json:"total"`
	TotalTax            float64       `json:"total_tax"`
	Items               []WcOrderItem `json:"line_items,omitempty" gorm:"-"`
}

func (WcOrder) TableName() string {
	return "wp_wc_orders"
}

// 訂單項目結構體
type WcOrderItem struct {
	ID           uint    `json:"id"`
	OrderID      uint    `json:"order_id"`
	ProductID    uint    `json:"product_id"`
	Name         string  `json:"name"`
	Quantity     int     `json:"quantity"`
	Subtotal     float64 `json:"subtotal"`
	SubtotalTax  float64 `json:"subtotal_tax"`
	Total        float64 `json:"total"`
	TotalTax     float64 `json:"total_tax"`
	Price        float64 `json:"price"`
	ProductSku   string  `json:"sku"`
	ProductImage string  `json:"image"`
}

type WcOrderFilter struct {
	Status     string
	CustomerID uint
	DateFrom   time.Time
	DateTo     time.Time
	Page       int
	PageSize   int
}

// WcOrderSummary 訂單摘要結構體（用於列表顯示）
type WcOrderSummary struct {
	ID                  uint    `json:"id"`
	DateCreated         string  `json:"date_created"`
	Status              string  `json:"status"`
	Total               float64 `json:"total"`
	PaymentMethodTitle  string  `json:"payment_method_title"`
	CustomerEmail       string  `json:"customer_email"`
	CustomerName        string  `json:"customer_name"`
	ShippingMethod      string  `json:"shipping_method"`
	ShippingMethodTitle string  `json:"shipping_method_title"`
}

// WcXeroOrderSync WooCommerce訂單與Xero同步記錄
type WcXeroOrderSync struct {
	ID            int64          `gorm:"primaryKey" json:"-"`
	UUID          string         `gorm:"<-:create" json:"uuid"`
	WcOrderID     uint           `gorm:"<-:create;index" json:"wc_order_id"`
	XeroInvoiceID string         `json:"xero_invoice_id,omitempty"`
	XeroInvoiceNo string         `json:"xero_invoice_no,omitempty"`
	SyncStatus    XeroSyncStatus `gorm:"default:pending" json:"sync_status"`
	ErrorMessage  string         `json:"error_message,omitempty"`
	LastSyncAt    *time.Time     `json:"last_sync_at,omitempty"`
	CreatedAt     time.Time      `gorm:"<-:create" json:"created_at"`
	UpdatedAt     time.Time      `json:"updated_at"`
}

func (WcXeroOrderSync) TableName() string {
	return "wc_xero_order_syncs"
}
